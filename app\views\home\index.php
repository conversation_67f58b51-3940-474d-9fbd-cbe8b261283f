<!-- Enhanced Hero Section -->
<section class="hero">
    <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-gradient-overlay"></div>
    </div>

    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>Premium Quality Products</span>
                </div>

                <h1 class="hero-title">
                    <span class="hero-title-main">Transform Your World with</span>
                    <span class="hero-title-accent">Cleanance Lab</span>
                </h1>

                <p class="hero-subtitle">
                    Discover cutting-edge products crafted with precision and care. From innovative solutions to everyday essentials, we bring you the finest quality that elevates your lifestyle.
                </p>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <div class="hero-stat-number">10K+</div>
                        <div class="hero-stat-label">Happy Customers</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">500+</div>
                        <div class="hero-stat-label">Premium Products</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">99%</div>
                        <div class="hero-stat-label">Satisfaction Rate</div>
                    </div>
                </div>

                <div class="hero-actions">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-primary btn-lg hero-cta-primary">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Explore Products</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="<?= UrlHelper::url('/about') ?>" class="btn btn-outline btn-lg hero-cta-secondary">
                        <i class="fas fa-play-circle"></i>
                        <span>Watch Story</span>
                    </a>
                </div>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-shipping-fast"></i>
                        <span>Free Shipping</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure Payment</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-medal"></i>
                        <span>Premium Quality</span>
                    </div>
                </div>
            </div>

            <div class="hero-visual-section">
                <div class="hero-main-visual">
                    <div class="hero-product-showcase">
                        <div class="hero-product-card hero-product-1">
                            <div class="product-image">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Lab Essentials</span>
                                <span class="product-price">$29.99</span>
                            </div>
                        </div>

                        <div class="hero-product-card hero-product-2">
                            <div class="product-image">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Premium Solutions</span>
                                <span class="product-price">$49.99</span>
                            </div>
                        </div>

                        <div class="hero-product-card hero-product-3">
                            <div class="product-image">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Research Tools</span>
                                <span class="product-price">$89.99</span>
                            </div>
                        </div>
                    </div>

                    <div class="hero-floating-elements">
                        <div class="floating-element element-1">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="floating-element element-2">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="floating-element element-3">
                            <i class="fas fa-molecular"></i>
                        </div>
                        <div class="floating-element element-4">✨</div>
                        <div class="floating-element element-5">🧪</div>
                        <div class="floating-element element-6">⚗️</div>
                    </div>
                </div>

                <div class="hero-trust-indicators">
                    <div class="trust-indicator">
                        <i class="fas fa-certificate"></i>
                        <span>ISO Certified</span>
                    </div>
                    <div class="trust-indicator">
                        <i class="fas fa-award"></i>
                        <span>Award Winning</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="hero-scroll-indicator">
        <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
        </div>
        <span>Discover More</span>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Shop by Category</h2>
            <p class="section-subtitle">Explore our curated collection of products</p>
        </div>

        <div class="categories-grid">
            <?php foreach ($categories as $category): ?>
                <a href="<?= UrlHelper::url('/products/' . $category['slug']) ?>" class="category-card">
                    <div class="category-icon">
                        <?php
                        $icons = [
                            'electronics' => 'fas fa-mobile-alt',
                            'fashion' => 'fas fa-tshirt',
                            'home-garden' => 'fas fa-home',
                            'sports' => 'fas fa-dumbbell',
                            'books' => 'fas fa-book'
                        ];
                        $icon = $icons[$category['slug']] ?? 'fas fa-tag';
                        ?>
                        <i class="<?= $icon ?>"></i>
                    </div>
                    <h3 class="category-name"><?= htmlspecialchars($category['name']) ?></h3>
                    <p class="category-description"><?= htmlspecialchars($category['description'] ?? 'Explore amazing products') ?></p>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                Featured Products
            </h2>
            <p class="section-subtitle">Handpicked products just for you</p>
        </div>

        <div class="products-grid grid-cols-5">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="product-card fade-in compact-card featured-card">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <?php if ($product['sale_price']): ?>
                            <div class="product-badge sale-badge">
                                <span>-<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%</span>
                            </div>
                        <?php endif; ?>
                        <?php if ($product['is_featured']): ?>
                            <div class="product-badge featured-badge">
                                <i class="fas fa-star"></i>
                            </div>
                        <?php endif; ?>
                        <div class="product-overlay">
                            <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>" title="Add to Wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn btn-primary btn-sm add-to-cart-quick" data-product-id="<?= $product['id'] ?>" title="Add to Cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            <?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?>
                        </div>
                        <h3 class="product-title">
                            <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                                <span class="discount-amount">
                                    Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?= $i <= 4 ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-count">(<?= rand(10, 50) ?>)</span>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary add-to-cart"
                                data-product-id="<?= $product['id'] ?>"
                                data-quantity="1">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-outline btn-lg view-all-btn">
                <i class="fas fa-th-large"></i>
                View All Products
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- Latest Products Section -->
<section class="latest-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-clock"></i>
                Latest Arrivals
            </h2>
            <p class="section-subtitle">Fresh products added to our collection</p>
        </div>

        <div class="products-grid grid-cols-4">
            <?php foreach ($latestProducts as $product): ?>
                <div class="product-card fade-in compact-card latest-card">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <div class="product-badge new-badge">
                            <i class="fas fa-sparkles"></i>
                            New
                        </div>
                        <div class="product-overlay">
                            <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>" title="Add to Wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn btn-primary btn-sm add-to-cart-quick" data-product-id="<?= $product['id'] ?>" title="Add to Cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            <?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?>
                        </div>
                        <h3 class="product-title">
                            <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                                <span class="discount-amount">
                                    Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?= $i <= 4 ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-count">(<?= rand(5, 25) ?>)</span>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary add-to-cart"
                                data-product-id="<?= $product['id'] ?>"
                                data-quantity="1">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="features-grid grid-cols-4">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h3 class="feature-title">Fast Shipping</h3>
                <p class="feature-description">Free shipping on orders over $100</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">Secure Payment</h3>
                <p class="feature-description">100% secure payment processing</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-undo"></i>
                </div>
                <h3 class="feature-title">Easy Returns</h3>
                <p class="feature-description">30-day return policy</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3 class="feature-title">24/7 Support</h3>
                <p class="feature-description">Round the clock customer support</p>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section">
    <div class="container">
        <div class="newsletter-content">
            <div class="newsletter-text">
                <h2 class="newsletter-title">Stay Updated</h2>
                <p class="newsletter-description">Subscribe to our newsletter for the latest products, exclusive offers, and updates.</p>
            </div>
            <form class="newsletter-form">
                <input type="email" placeholder="Enter your email address" required>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Subscribe
                </button>
            </form>
        </div>
    </div>
</section>

<style>
    /* Enhanced Hero Section */
    .hero {
        min-height: 100vh;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;
        background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-card) 50%, var(--dark-surface) 100%);
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
    }

    .hero-particles {
        position: absolute;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(2px 2px at 20px 30px, rgba(126, 87, 194, 0.3), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(126, 87, 194, 0.2), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(126, 87, 194, 0.4), transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(126, 87, 194, 0.3), transparent),
            radial-gradient(2px 2px at 160px 30px, rgba(126, 87, 194, 0.2), transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: particleFloat 20s linear infinite;
    }

    .hero-gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 30%, rgba(126, 87, 194, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(126, 87, 194, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, rgba(126, 87, 194, 0.05) 0%, transparent 100%);
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        align-items: center;
        position: relative;
        z-index: 1;
        padding: var(--spacing-3xl) 0;
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-xl);
        font-size: var(--font-size-sm);
        font-weight: 600;
        margin-bottom: var(--spacing-lg);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
        animation: fadeInUp 0.8s ease-out;
    }

    .hero-title {
        margin-bottom: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .hero-title-main {
        display: block;
        font-size: var(--font-size-3xl);
        font-weight: 400;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-sm);
        line-height: 1.2;
    }

    .hero-title-accent {
        display: block;
        font-size: 4rem;
        font-weight: 800;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.1;
        margin-bottom: var(--spacing-md);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        line-height: 1.7;
        margin-bottom: var(--spacing-xl);
        max-width: 90%;
        animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    .hero-stats {
        display: flex;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        animation: fadeInUp 0.8s ease-out 0.6s both;
    }

    .hero-stat {
        text-align: center;
    }

    .hero-stat-number {
        font-size: var(--font-size-2xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .hero-stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .hero-actions {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        animation: fadeInUp 0.8s ease-out 0.8s both;
    }

    .hero-cta-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border: none;
        color: var(--white);
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-fast);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
        position: relative;
        overflow: hidden;
    }

    .hero-cta-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .hero-cta-primary:hover::before {
        left: 100%;
    }

    .hero-cta-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: var(--white);
    }

    .hero-cta-secondary {
        background: transparent;
        border: 2px solid var(--primary-purple);
        color: var(--primary-purple);
        padding: calc(var(--spacing-lg) - 2px) calc(var(--spacing-xl) - 2px);
        border-radius: var(--radius-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-fast);
    }

    .hero-cta-secondary:hover {
        background: var(--primary-purple);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .hero-features {
        display: flex;
        gap: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 1s both;
    }

    .hero-feature {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    .hero-feature i {
        color: var(--primary-purple);
        font-size: var(--font-size-base);
    }

    .hero-visual-section {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .hero-main-visual {
        position: relative;
        width: 100%;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-product-showcase {
        position: relative;
        width: 400px;
        height: 400px;
    }

    .hero-product-card {
        position: absolute;
        width: 120px;
        height: 140px;
        background: var(--dark-card);
        border: 2px solid var(--primary-purple);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.2);
        transition: all var(--transition-fast);
        animation: productFloat 6s ease-in-out infinite;
    }

    .hero-product-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .hero-product-2 {
        top: 10%;
        right: 15%;
        animation-delay: 2s;
    }

    .hero-product-3 {
        bottom: 20%;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 4s;
    }

    .hero-product-card:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.3);
    }

    .hero-product-card .product-image {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--font-size-xl);
    }

    .hero-product-card .product-info {
        text-align: center;
    }

    .hero-product-card .product-name {
        display: block;
        font-size: var(--font-size-xs);
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
    }

    .hero-product-card .product-price {
        display: block;
        font-size: var(--font-size-sm);
        color: var(--primary-purple);
        font-weight: 700;
    }

    .hero-floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .floating-element {
        position: absolute;
        font-size: var(--font-size-xl);
        color: var(--primary-purple);
        opacity: 0.6;
        animation: elementFloat 8s ease-in-out infinite;
    }

    .element-1 {
        top: 15%;
        left: 5%;
        animation-delay: 0s;
    }

    .element-2 {
        top: 25%;
        right: 5%;
        animation-delay: 1s;
    }

    .element-3 {
        bottom: 30%;
        left: 15%;
        animation-delay: 2s;
    }

    .element-4 {
        top: 60%;
        right: 25%;
        animation-delay: 3s;
    }

    .element-5 {
        bottom: 15%;
        right: 10%;
        animation-delay: 4s;
    }

    .element-6 {
        top: 40%;
        left: 80%;
        animation-delay: 5s;
    }

    .hero-trust-indicators {
        display: flex;
        gap: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 1.2s both;
    }

    .trust-indicator {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: rgba(126, 87, 194, 0.1);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-lg);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
        border: 1px solid rgba(126, 87, 194, 0.2);
    }

    .trust-indicator i {
        color: var(--primary-purple);
    }

    .hero-scroll-indicator {
        position: absolute;
        bottom: var(--spacing-xl);
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        animation: fadeInUp 0.8s ease-out 1.4s both;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .hero-scroll-indicator:hover {
        color: var(--primary-purple);
        transform: translateX(-50%) translateY(-5px);
    }

    .scroll-arrow {
        width: 40px;
        height: 40px;
        border: 2px solid currentColor;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: bounce 2s infinite;
    }

    /* Hero Animations */
    @keyframes particleFloat {
        0% {
            transform: translateY(0px) translateX(0px);
        }

        33% {
            transform: translateY(-10px) translateX(5px);
        }

        66% {
            transform: translateY(5px) translateX(-5px);
        }

        100% {
            transform: translateY(0px) translateX(0px);
        }
    }

    @keyframes productFloat {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        50% {
            transform: translateY(-15px) rotate(2deg);
        }
    }

    @keyframes elementFloat {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.6;
        }

        25% {
            transform: translateY(-10px) rotate(5deg);
            opacity: 0.8;
        }

        50% {
            transform: translateY(-5px) rotate(-3deg);
            opacity: 0.4;
        }

        75% {
            transform: translateY(-12px) rotate(7deg);
            opacity: 0.7;
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-10px);
        }

        60% {
            transform: translateY(-5px);
        }
    }

    /* Categories Section */
    .categories-section {
        padding: var(--spacing-2xl) 0;
        background-color: var(--dark-bg);
    }

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .category-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-fast);
    }

    .category-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
        color: var(--text-primary);
    }

    .category-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-2xl);
        color: var(--white);
    }

    .category-name {
        font-size: var(--font-size-xl);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .category-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Featured & Latest Sections */
    .featured-section,
    .latest-section {
        padding: var(--spacing-3xl) 0;
        position: relative;
    }

    .featured-section {
        background-color: var(--dark-card);
    }

    .latest-section {
        background-color: var(--dark-bg);
    }

    .featured-section .container,
    .latest-section .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Section Spacing */
    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-3xl);
        position: relative;
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .section-title i {
        color: var(--primary-purple);
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Grid Container Improvements */
    .products-grid {
        display: grid;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        align-items: start;
        justify-items: stretch;
        width: 100%;
    }

    .products-grid.grid-cols-5 {
        grid-template-columns: repeat(5, 1fr);
        max-width: 1200px;
        margin: 0 auto var(--spacing-xl);
    }

    .products-grid.grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1000px;
        margin: 0 auto var(--spacing-xl);
    }

    /* Ensure all cards have equal height */
    .products-grid .product-card {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    /* Compact Product Cards for Home Page */
    .compact-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-fast);
        box-shadow: var(--shadow-md);
    }

    .compact-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .compact-card .product-image {
        aspect-ratio: 1;
        height: 200px;
        position: relative;
        overflow: hidden;
    }

    .compact-card .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-fast);
    }

    .compact-card:hover .product-image img {
        transform: scale(1.05);
    }

    .compact-card .product-content {
        padding: var(--spacing-md);
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .compact-card .product-title {
        font-size: var(--font-size-sm);
        line-height: 1.3;
        margin-bottom: var(--spacing-xs);
        min-height: 2.6em;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .compact-card .product-category {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-xs);
        min-height: 1.2em;
    }

    .compact-card .product-price {
        margin-bottom: var(--spacing-sm);
        min-height: 1.5em;
    }

    .compact-card .product-price-current {
        font-size: var(--font-size-md);
        font-weight: 600;
    }

    .compact-card .product-price-original {
        font-size: var(--font-size-xs);
    }

    .compact-card .product-rating {
        margin-bottom: var(--spacing-sm);
        min-height: 1.2em;
    }

    .compact-card .product-actions {
        margin-top: auto;
        padding-top: var(--spacing-sm);
    }

    .compact-card .product-actions .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: opacity var(--transition-fast);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    /* Features Section */
    .features-section {
        padding: var(--spacing-2xl) 0;
        background-color: var(--dark-card);
    }

    .feature-card {
        text-align: center;
        padding: var(--spacing-lg);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-xl);
        color: var(--white);
    }

    .feature-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .feature-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Newsletter Section */
    .newsletter-section {
        padding: var(--spacing-2xl) 0;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        color: var(--white);
    }

    .newsletter-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
    }

    .newsletter-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-md);
    }

    .newsletter-description {
        font-size: var(--font-size-lg);
        opacity: 0.9;
        line-height: 1.6;
    }

    .newsletter-form {
        display: flex;
        gap: var(--spacing-sm);
    }

    .newsletter-form input {
        flex: 1;
        padding: var(--spacing-md);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--white);
    }

    .newsletter-form input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form input:focus {
        outline: none;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .newsletter-form .btn {
        background-color: var(--white);
        color: var(--primary-purple);
        border: none;
    }

    .newsletter-form .btn:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }

    /* Enhanced Responsive Design System */

    /* Large Desktop (1200px+) - Default grid layouts */

    /* Desktop (1024px - 1199px) */
    @media (max-width: 1199px) {
        .container {
            max-width: 1140px;
            padding: 0 var(--spacing-lg);
        }

        .grid-cols-5 {
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-lg);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-lg);
        }

        .hero-content {
            gap: var(--spacing-2xl);
        }

        .section-header {
            margin-bottom: var(--spacing-xl);
        }
    }

    /* Tablet Landscape (768px - 1023px) */
    @media (max-width: 1023px) {
        .container {
            max-width: 960px;
            padding: 0 var(--spacing-md);
        }

        .grid-cols-5 {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }

        .features-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-lg);
        }

        .categories-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-lg);
        }

        .products-grid {
            gap: var(--spacing-md);
        }

        /* Adjust section padding */
        .section {
            padding: var(--spacing-2xl) 0;
        }

        .hero {
            min-height: 85vh;
        }

        .hero-content {
            gap: var(--spacing-xl);
        }

        .section-header {
            margin-bottom: var(--spacing-lg);
        }

        .section-title {
            font-size: var(--font-size-2xl);
        }

        .section-subtitle {
            font-size: var(--font-size-base);
        }
    }

    /* Tablet Portrait (481px - 767px) */
    @media (max-width: 767px) {
        .container {
            max-width: 720px;
            padding: 0 var(--spacing-md);
        }

        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }

        .features-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }

        .categories-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .hero {
            min-height: 90vh;
            padding: var(--spacing-xl) 0;
        }

        .hero-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
            text-align: center;
        }

        .hero-title-main {
            font-size: var(--font-size-xl);
        }

        .hero-title-accent {
            font-size: var(--font-size-4xl);
        }

        .hero-subtitle {
            font-size: var(--font-size-base);
            max-width: 100%;
        }

        .hero-stats {
            justify-content: center;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
        }

        .hero-actions {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            width: 100%;
            max-width: 320px;
            justify-content: center;
            padding: var(--spacing-lg) var(--spacing-xl);
        }

        .hero-features {
            justify-content: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .hero-main-visual {
            height: 350px;
        }

        .hero-product-showcase {
            width: 300px;
            height: 300px;
        }

        .hero-product-card {
            width: 100px;
            height: 120px;
            padding: var(--spacing-sm);
        }

        .hero-product-card .product-image {
            width: 50px;
            height: 50px;
            font-size: var(--font-size-lg);
        }

        .hero-trust-indicators {
            justify-content: center;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        /* Section adjustments */
        .section {
            padding: var(--spacing-xl) 0;
        }

        .section-header {
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .section-title {
            font-size: var(--font-size-xl);
        }

        .section-subtitle {
            font-size: var(--font-size-sm);
        }

        /* Product card adjustments */
        .product-card {
            border-radius: var(--radius-md);
        }

        .compact-card .product-image {
            height: 180px;
        }

        .compact-card .product-content {
            padding: var(--spacing-sm);
        }

        .compact-card .product-title {
            font-size: var(--font-size-sm);
            min-height: 2.6em;
        }

        .compact-card .product-price {
            font-size: var(--font-size-base);
        }

        .compact-card .product-actions .btn {
            font-size: var(--font-size-sm);
            padding: var(--spacing-sm) var(--spacing-md);
        }

        /* Feature cards */
        .feature-card {
            padding: var(--spacing-lg);
            text-align: center;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            font-size: var(--font-size-xl);
            margin: 0 auto var(--spacing-md);
        }

        .feature-title {
            font-size: var(--font-size-lg);
        }

        .feature-description {
            font-size: var(--font-size-sm);
        }

        /* Newsletter section */
        .newsletter-section {
            padding: var(--spacing-xl) 0;
        }

        .newsletter-form {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .newsletter-form input {
            width: 100%;
        }

        .newsletter-form .btn {
            width: 100%;
        }
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .newsletter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
    }

    /* Responsive Product Grids */
    .grid-cols-5,
    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .compact-card .product-image {
        height: 180px;
    }

    .compact-card .product-content {
        padding: var(--spacing-sm);
    }

    /* Mobile Landscape (481px - 640px) */
    @media (max-width: 640px) {
        .container {
            padding: 0 var(--spacing-sm);
        }

        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .features-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            max-width: 280px;
        }

        .section {
            padding: var(--spacing-lg) 0;
        }
    }

    /* Mobile Portrait (320px - 480px) */
    @media (max-width: 480px) {
        .container {
            padding: 0 var(--spacing-sm);
            max-width: 100%;
        }

        /* Hero Section Mobile Optimization */
        .hero {
            min-height: 80vh;
            padding: var(--spacing-lg) 0;
        }

        .hero-content {
            gap: var(--spacing-lg);
            padding: var(--spacing-md) 0;
        }

        .hero-badge {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .hero-title-main {
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-xs);
        }

        .hero-title-accent {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-sm);
        }

        .hero-subtitle {
            font-size: var(--font-size-sm);
            line-height: 1.5;
            margin-bottom: var(--spacing-lg);
        }

        .hero-stats {
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .hero-stat {
            min-width: 80px;
        }

        .hero-stat-number {
            font-size: var(--font-size-xl);
        }

        .hero-stat-label {
            font-size: var(--font-size-xs);
        }

        .hero-actions {
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-sm);
            max-width: 100%;
            min-height: 48px;
        }

        .hero-features {
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .hero-feature {
            font-size: var(--font-size-xs);
        }

        .hero-main-visual {
            height: 250px;
        }

        .hero-product-showcase {
            width: 200px;
            height: 200px;
        }

        .hero-product-card {
            width: 80px;
            height: 100px;
            padding: var(--spacing-xs);
        }

        .hero-product-card .product-image {
            width: 40px;
            height: 40px;
            font-size: var(--font-size-base);
        }

        .hero-product-card .product-name {
            font-size: 10px;
        }

        .hero-product-card .product-price {
            font-size: var(--font-size-xs);
        }

        .floating-element {
            font-size: var(--font-size-base);
        }

        .trust-indicator {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
        }

        .hero-scroll-indicator {
            bottom: var(--spacing-md);
            font-size: var(--font-size-xs);
        }

        .scroll-arrow {
            width: 30px;
            height: 30px;
        }

        /* Section Optimizations */
        .section {
            padding: var(--spacing-lg) 0;
        }

        .section-header {
            margin-bottom: var(--spacing-md);
        }

        .section-title {
            font-size: var(--font-size-lg);
            line-height: 1.3;
        }

        .section-subtitle {
            font-size: var(--font-size-xs);
            line-height: 1.4;
        }

        /* Grid Layouts */
        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }

        .categories-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }

        .features-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        /* Product Cards Mobile Optimization */
        .product-card {
            border-radius: var(--radius-sm);
            min-height: 280px;
        }

        .compact-card .product-image {
            height: 140px;
        }

        .compact-card .product-content {
            padding: var(--spacing-xs);
        }

        .compact-card .product-title {
            font-size: var(--font-size-xs);
            min-height: 2.4em;
            line-height: 1.2;
        }

        .compact-card .product-price {
            font-size: var(--font-size-sm);
            font-weight: 700;
        }

        .compact-card .product-actions {
            margin-top: var(--spacing-xs);
        }

        .compact-card .product-actions .btn {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            min-height: 36px;
        }

        /* Feature Cards */
        .feature-card {
            padding: var(--spacing-md);
            text-align: center;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-sm);
        }

        .feature-title {
            font-size: var(--font-size-base);
            margin-bottom: var(--spacing-xs);
        }

        .feature-description {
            font-size: var(--font-size-xs);
            line-height: 1.4;
        }

        /* Newsletter Section */
        .newsletter-section {
            padding: var(--spacing-lg) 0;
        }

        .newsletter-content {
            text-align: center;
        }

        .newsletter-title {
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-sm);
        }

        .newsletter-description {
            font-size: var(--font-size-xs);
            margin-bottom: var(--spacing-lg);
        }

        .newsletter-form {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .newsletter-form input {
            width: 100%;
            padding: var(--spacing-md);
            font-size: var(--font-size-sm);
            min-height: 48px;
        }

        .newsletter-form .btn {
            width: 100%;
            padding: var(--spacing-md);
            font-size: var(--font-size-sm);
            min-height: 48px;
        }
    }

    /* Extra Small Mobile (320px and below) */
    @media (max-width: 320px) {
        .container {
            padding: 0 var(--spacing-xs);
        }

        .hero {
            min-height: 75vh;
            padding: var(--spacing-md) 0;
        }

        .hero-title-accent {
            font-size: var(--font-size-2xl);
        }

        .hero-stats {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .hero-stat {
            min-width: auto;
        }

        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: 1fr;
            gap: var(--spacing-xs);
        }

        .product-card {
            min-height: 260px;
        }

        .compact-card .product-image {
            height: 120px;
        }

        .section {
            padding: var(--spacing-md) 0;
        }

        .section-title {
            font-size: var(--font-size-base);
        }

        .feature-card {
            padding: var(--spacing-sm);
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            font-size: var(--font-size-base);
        }
    }

    /* Touch Interface Enhancements */

    /* Ensure all interactive elements meet minimum touch target size */
    .btn,
    .nav-item,
    .mobile-action-btn,
    .search-toggle,
    .modal-close,
    .quantity-btn,
    .product-card .product-actions .btn,
    .overlay-actions .btn {
        min-height: 44px;
        min-width: 44px;
        position: relative;
        overflow: hidden;
    }

    /* Enhanced touch feedback for buttons */
    .btn {
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .btn:active {
        transform: scale(0.98);
    }

    /* Touch ripple effect */
    .btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
        pointer-events: none;
        z-index: 1;
    }

    .btn:active::before {
        width: 100px;
        height: 100px;
    }

    /* Enhanced spacing for touch targets */
    .hero-actions {
        gap: var(--spacing-lg);
    }

    .hero-features {
        gap: var(--spacing-lg);
    }

    .product-actions {
        gap: var(--spacing-md);
        padding: var(--spacing-sm);
    }

    .overlay-actions {
        gap: var(--spacing-md);
    }

    /* Touch-friendly product cards */
    .product-card {
        cursor: pointer;
        -webkit-tap-highlight-color: transparent;
        transition: all 0.2s ease;
    }

    .product-card:active {
        transform: scale(0.98);
    }

    /* Enhanced mobile action buttons */
    .mobile-action-btn {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-lg);
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        -webkit-tap-highlight-color: transparent;
    }

    .mobile-action-btn:active {
        transform: scale(0.9);
        background: var(--primary-purple);
    }

    /* Touch-friendly navigation */
    .nav-item {
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .nav-item:active {
        transform: scale(0.95);
        background: rgba(126, 87, 194, 0.2);
    }

    /* Enhanced search toggle */
    .search-toggle {
        width: 56px;
        height: 56px;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .search-toggle:active {
        transform: scale(0.9);
    }

    /* Touch-friendly modal controls */
    .modal-close {
        width: 48px;
        height: 48px;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .modal-close:active {
        transform: scale(0.9);
    }

    .quantity-btn {
        width: 48px;
        height: 48px;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .quantity-btn:active {
        transform: scale(0.9);
        background: var(--primary-purple);
        color: white;
    }

    /* Touch feedback for form elements */
    input,
    textarea,
    select {
        min-height: 48px;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    input:focus,
    textarea:focus,
    select:focus {
        transform: scale(1.02);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.2);
    }

    /* Enhanced touch targets for small elements */
    .suggestion-tag {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .suggestion-tag:active {
        transform: scale(0.95);
    }

    .trust-indicator {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-lg);
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    /* Enhanced Product Cards */
    .featured-card,
    .latest-card {
        position: relative;
        overflow: hidden;
        border: 1px solid var(--border-color);
        transition: all var(--transition-fast);
    }

    .featured-card {
        border-color: var(--primary-purple);
        box-shadow: 0 4px 20px rgba(126, 87, 194, 0.1);
    }

    .featured-card:hover {
        border-color: var(--primary-light);
        box-shadow: 0 8px 30px rgba(126, 87, 194, 0.2);
        transform: translateY(-8px);
    }

    .latest-card {
        border-color: var(--success-color);
        box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
    }

    .latest-card:hover {
        border-color: var(--success-color);
        box-shadow: 0 8px 30px rgba(16, 185, 129, 0.2);
        transform: translateY(-8px);
    }

    /* Enhanced Product Badges */
    .product-badge {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
        z-index: 2;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .sale-badge {
        background: linear-gradient(135deg, var(--error-color), #dc2626);
        color: white;
        animation: pulse 2s infinite;
    }

    .featured-badge {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: auto;
    }

    .new-badge {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        animation: bounce 2s infinite;
    }

    /* Product Ratings */
    .product-rating {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
    }

    .stars {
        display: flex;
        gap: 2px;
    }

    .stars i {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
    }

    .stars i.filled {
        color: #fbbf24;
    }

    .rating-count {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
    }

    /* Enhanced Product Overlay */
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: all var(--transition-fast);
        backdrop-filter: blur(2px);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    .product-overlay .btn {
        transform: translateY(20px);
        transition: all var(--transition-fast);
    }

    .product-card:hover .product-overlay .btn {
        transform: translateY(0);
    }

    .product-overlay .btn:nth-child(1) {
        transition-delay: 0.1s;
    }

    .product-overlay .btn:nth-child(2) {
        transition-delay: 0.2s;
    }

    .product-overlay .btn:nth-child(3) {
        transition-delay: 0.3s;
    }

    /* Enhanced Product Content */
    .product-category {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }

    .product-category i {
        color: var(--primary-purple);
        font-size: var(--font-size-xs);
    }

    .discount-amount {
        background: var(--success-color);
        color: white;
        padding: 2px var(--spacing-xs);
        border-radius: var(--radius-xs);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    /* Enhanced View All Button */
    .view-all-btn {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border: none;
        color: white;
        padding: var(--spacing-lg) var(--spacing-2xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        transition: all var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
    }

    .view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: white;
    }

    /* Animations */
    @keyframes pulse {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-5px);
        }

        60% {
            transform: translateY(-3px);
        }
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Mobile Touch Enhancements */
    @media (max-width: 768px) {

        /* Enhanced touch-friendly elements */
        .btn,
        .nav-item,
        .mobile-action-btn,
        .search-toggle,
        .modal-close,
        .quantity-btn {
            min-height: 48px;
            min-width: 48px;
        }

        .mobile-swipe-indicator {
            display: block;
        }

        .modal-body {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
        }

        .modal-content {
            margin: var(--spacing-sm);
            max-height: 95vh;
        }

        .modal-actions {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .modal-actions .btn {
            min-height: 48px;
            font-size: var(--font-size-base);
        }

        .modal-product-features {
            padding: var(--spacing-md);
        }

        .enhanced-card {
            border-radius: var(--radius-md);
        }

        .enhanced-card:hover {
            transform: none;
            box-shadow: 0 4px 15px rgba(126, 87, 194, 0.1);
        }

        /* Enhanced touch-friendly product cards */
        .product-card {
            cursor: pointer;
            -webkit-tap-highlight-color: transparent;
            min-height: 300px;
        }

        .product-card:active {
            transform: scale(0.98);
        }

        /* Improved mobile product grid */
        .products-grid {
            gap: var(--spacing-md);
            padding: 0 var(--spacing-sm);
        }

        .products-grid.grid-cols-5,
        .products-grid.grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
        }

        /* Enhanced mobile-optimized buttons */
        .btn {
            min-height: 48px;
            font-size: var(--font-size-base);
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
        }

        /* Touch-friendly spacing */
        .hero-actions .btn {
            margin-bottom: var(--spacing-sm);
        }

        .product-actions {
            padding: var(--spacing-md);
            gap: var(--spacing-md);
        }

        .product-actions .btn {
            min-height: 44px;
            padding: var(--spacing-sm) var(--spacing-md);
        }

        /* Enhanced swipe gesture support */
        .products-grid {
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
            scroll-behavior: smooth;
        }

        .products-grid::-webkit-scrollbar {
            display: none;
        }

        .product-card {
            scroll-snap-align: start;
            flex-shrink: 0;
        }

        /* Touch feedback for mobile navigation */
        .mobile-bottom-nav .nav-item {
            min-height: 60px;
            padding: var(--spacing-sm);
        }

        .mobile-bottom-nav .nav-item:active {
            background: rgba(126, 87, 194, 0.1);
        }

        /* Enhanced mobile search */
        .mobile-search-input {
            min-height: 48px;
            font-size: 16px;
            /* Prevent zoom on iOS */
        }

        .search-submit {
            width: 48px;
            height: 48px;
        }

        /* Touch-friendly newsletter form */
        .newsletter-form input {
            min-height: 48px;
            font-size: 16px;
            /* Prevent zoom on iOS */
        }

        .newsletter-form .btn {
            min-height: 48px;
        }

        /* Prevent text selection on touch */
        .product-card,
        .btn,
        .nav-item {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Enhanced touch targets for small screens */
        .hero-feature,
        .trust-indicator,
        .suggestion-tag {
            min-height: 44px;
            padding: var(--spacing-sm) var(--spacing-md);
        }
    }

    @media (max-width: 480px) {

        /* Extra touch-friendly enhancements for small screens */
        .btn {
            min-height: 50px;
            padding: var(--spacing-md) var(--spacing-lg);
        }

        .mobile-action-btn {
            width: 50px;
            height: 50px;
        }

        .search-toggle {
            width: 60px;
            height: 60px;
        }

        .nav-item {
            min-height: 64px;
            padding: var(--spacing-md);
        }

        /* Larger touch targets for critical actions */
        .hero-cta-primary,
        .hero-cta-secondary {
            min-height: 52px;
            padding: var(--spacing-lg) var(--spacing-xl);
        }

        .modal-actions .btn {
            min-height: 50px;
            padding: var(--spacing-lg);
        }

        /* Enhanced spacing for touch */
        .product-actions {
            gap: var(--spacing-lg);
        }

        .hero-actions {
            gap: var(--spacing-lg);
        }

        .hero-features {
            gap: var(--spacing-lg);
        }
    }

    /* Content Adaptation for Mobile */

    /* Enhanced Typography for Mobile Readability */
    @media (max-width: 768px) {

        /* Improved text hierarchy */
        h1,
        .hero-title-accent {
            font-size: clamp(2rem, 8vw, 3rem);
            line-height: 1.2;
            letter-spacing: -0.02em;
        }

        h2,
        .section-title {
            font-size: clamp(1.5rem, 6vw, 2rem);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        h3,
        .hero-title-main {
            font-size: clamp(1.25rem, 5vw, 1.5rem);
            line-height: 1.4;
        }

        p,
        .hero-subtitle,
        .section-subtitle {
            font-size: clamp(0.875rem, 4vw, 1rem);
            line-height: 1.6;
            max-width: 100%;
        }

        /* Enhanced readability */
        .hero-subtitle {
            margin-bottom: var(--spacing-lg);
            text-align: center;
            padding: 0 var(--spacing-md);
        }

        .section-subtitle {
            margin-bottom: var(--spacing-md);
            text-align: center;
            padding: 0 var(--spacing-sm);
        }

        /* Improved content spacing */
        .hero-content {
            padding: var(--spacing-lg) var(--spacing-md);
        }

        .section-header {
            padding: 0 var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        /* Better content hierarchy */
        .hero-badge {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
            padding: var(--spacing-xs) var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .hero-stats {
            margin: var(--spacing-lg) 0;
            padding: 0 var(--spacing-md);
        }

        .hero-stat-number {
            font-size: clamp(1.5rem, 6vw, 2rem);
        }

        .hero-stat-label {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
        }

        .hero-features {
            margin: var(--spacing-lg) 0;
            padding: 0 var(--spacing-md);
        }

        .hero-feature {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
        }
    }

    /* Image Optimization for Mobile */
    @media (max-width: 768px) {

        /* Responsive images */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            transition: transform 0.3s ease;
        }

        /* Lazy loading optimization */
        img[loading="lazy"] {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        img[loading="lazy"].loaded {
            opacity: 1;
        }

        /* Placeholder for loading images */
        .product-image {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            animation: loading-shimmer 1.5s infinite linear;
        }

        .product-image.loaded {
            background: none;
            animation: none;
        }

        /* Hero visual optimization */
        .hero-main-visual {
            height: clamp(200px, 50vw, 300px);
        }

        .hero-product-showcase {
            width: clamp(150px, 40vw, 250px);
            height: clamp(150px, 40vw, 250px);
        }

        .hero-product-card {
            width: clamp(60px, 15vw, 80px);
            height: clamp(80px, 20vw, 100px);
        }

        .hero-product-card .product-image {
            width: clamp(30px, 8vw, 40px);
            height: clamp(30px, 8vw, 40px);
        }
    }

    /* Content Layout Improvements */
    @media (max-width: 480px) {

        /* Ultra-mobile typography */
        .hero-title-accent {
            font-size: clamp(1.75rem, 10vw, 2.5rem);
            margin-bottom: var(--spacing-sm);
        }

        .hero-title-main {
            font-size: clamp(1rem, 5vw, 1.25rem);
            margin-bottom: var(--spacing-xs);
        }

        .hero-subtitle {
            font-size: clamp(0.875rem, 4vw, 1rem);
            line-height: 1.5;
            margin-bottom: var(--spacing-md);
        }

        /* Compact content spacing */
        .hero-content {
            padding: var(--spacing-md) var(--spacing-sm);
        }

        .section {
            padding: var(--spacing-md) 0;
        }

        .section-header {
            padding: 0 var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .section-title {
            font-size: clamp(1.25rem, 7vw, 1.75rem);
            margin-bottom: var(--spacing-xs);
        }

        .section-subtitle {
            font-size: clamp(0.75rem, 4vw, 0.875rem);
            line-height: 1.4;
        }

        /* Optimized hero stats layout */
        .hero-stats {
            flex-direction: row;
            justify-content: space-around;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .hero-stat {
            text-align: center;
            min-width: auto;
            flex: 1;
        }

        .hero-stat-number {
            font-size: clamp(1.25rem, 6vw, 1.5rem);
            margin-bottom: var(--spacing-xs);
        }

        .hero-stat-label {
            font-size: clamp(0.625rem, 3vw, 0.75rem);
            line-height: 1.2;
        }

        /* Improved feature layout */
        .hero-features {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .hero-feature {
            font-size: clamp(0.75rem, 3.5vw, 0.875rem);
            text-align: center;
        }

        /* Trust indicators optimization */
        .hero-trust-indicators {
            flex-direction: column;
            gap: var(--spacing-xs);
            align-items: center;
        }

        .trust-indicator {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
            padding: var(--spacing-xs) var(--spacing-sm);
            min-height: 36px;
        }
    }

    /* Loading Animation */
    @keyframes loading-shimmer {
        0% {
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        100% {
            background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px;
        }
    }

    /* Accessibility Improvements */
    @media (prefers-reduced-motion: reduce) {
        .product-image {
            animation: none;
        }

        img {
            transition: none;
        }

        .hero-product-card {
            animation: none;
        }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .hero-title-accent {
            background: none;
            color: var(--text-primary);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .product-badge {
            border: 2px solid white;
        }

        .trust-indicator {
            border: 1px solid var(--text-primary);
        }
    }
</style>

<script>
    // Enhanced Product Interactions and Hero Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Hero scroll indicator functionality
        const scrollIndicator = document.querySelector('.hero-scroll-indicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', function() {
                const categoriesSection = document.querySelector('.categories-section');
                if (categoriesSection) {
                    categoriesSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        }

        // Parallax effect for hero background
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const heroBackground = document.querySelector('.hero-background');

            if (hero && heroBackground) {
                const heroHeight = hero.offsetHeight;
                const scrollPercent = scrolled / heroHeight;

                if (scrollPercent <= 1) {
                    heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
                    hero.style.opacity = Math.max(0.3, 1 - scrollPercent * 0.7);
                }
            }
        });

        // Animate hero stats on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.hero-stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
                        const suffix = finalValue.replace(/[\d]/g, '');

                        animateNumber(stat, 0, numericValue, suffix, 2000);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);

        const heroStats = document.querySelector('.hero-stats');
        if (heroStats) {
            statsObserver.observe(heroStats);
        }
        // Add to Cart functionality
        document.querySelectorAll('.add-to-cart, .add-to-cart-quick').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                const quantity = this.dataset.quantity || 1;

                addToCart(productId, quantity);
            });
        });

        // Add to Wishlist functionality
        document.querySelectorAll('.add-to-wishlist').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                addToWishlist(productId);
            });
        });

        // Quick View functionality
        document.querySelectorAll('.quick-view').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                showQuickView(productId);
            });
        });

        // Staggered animation for product cards
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    });

    // Add to Cart Function
    function addToCart(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        fetch('<?= UrlHelper::url('/cart/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart successfully!', 'success');
                    updateCartCount();
                } else {
                    showNotification(data.message || 'Failed to add to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to cart', 'error');
            });
    }

    // Add to Wishlist Function
    function addToWishlist(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);

        fetch('<?= UrlHelper::url('/wishlist/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                    updateWishlistCount();
                } else {
                    showNotification(data.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to wishlist', 'error');
            });
    }

    // Quick View Function
    function showQuickView(productId) {
        // For now, redirect to product detail page
        // In a real implementation, this would show a modal
        window.location.href = `<?= UrlHelper::url('/product/') ?>${productId}`;
    }

    // Notification System
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ${getNotificationStyle(type)}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    function getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'exclamation-circle';
            case 'warning':
                return 'exclamation-triangle';
            default:
                return 'info-circle';
        }
    }

    function getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'background: linear-gradient(135deg, #10b981, #059669);';
            case 'error':
                return 'background: linear-gradient(135deg, #ef4444, #dc2626);';
            case 'warning':
                return 'background: linear-gradient(135deg, #f59e0b, #d97706);';
            default:
                return 'background: linear-gradient(135deg, #3b82f6, #2563eb);';
        }
    }

    // Number animation function for hero stats
    function animateNumber(element, start, end, suffix, duration) {
        const startTime = performance.now();

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (end - start) * easeOutQuart);

            element.textContent = current + suffix;

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // Enhanced button hover effects
    function addButtonEffects() {
        const buttons = document.querySelectorAll('.hero-cta-primary, .hero-cta-secondary');

        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Initialize enhanced effects
    addButtonEffects();

    // Update counters (placeholder functions)
    function updateCartCount() {
        // This would typically fetch the current cart count from the server
        console.log('Cart updated');
    }

    function updateWishlistCount() {
        // This would typically fetch the current wishlist count from the server
        console.log('Wishlist updated');
    }

    // Enhanced Mobile Navigation Functions
    function initMobileNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const currentPath = window.location.pathname;

        // Update active state based on current page
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && currentPath.includes(href.split('/').pop())) {
                navItems.forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
            }
        });

        // Add enhanced click animations with haptic feedback
        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                // Add ripple effect
                const ripple = document.createElement('div');
                ripple.className = 'nav-ripple';

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';

                this.appendChild(ripple);

                // Haptic feedback for supported devices
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });

            // Add touch feedback
            item.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });

            item.addEventListener('touchcancel', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Update cart badge dynamically
        updateCartBadge();

        // Auto-hide navigation on scroll (optional)
        let lastScrollTop = 0;
        const mobileNav = document.querySelector('.mobile-bottom-nav');

        if (mobileNav) {
            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down
                    mobileNav.style.transform = 'translateY(100%)';
                } else {
                    // Scrolling up
                    mobileNav.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
            }, false);
        }
    }

    function updateCartBadge() {
        const cartBadge = document.querySelector('.cart-badge');
        if (cartBadge) {
            // Simulate cart count - replace with actual implementation
            const cartCount = getCartCount();
            if (cartCount > 0) {
                cartBadge.textContent = cartCount > 99 ? '99+' : cartCount;
                cartBadge.style.display = 'flex';

                // Add pulse animation for new items
                cartBadge.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    cartBadge.style.animation = '';
                }, 500);
            } else {
                cartBadge.style.display = 'none';
            }
        }
    }

    function getCartCount() {
        // Placeholder - implement actual cart count logic
        // This could fetch from localStorage, sessionStorage, or make an API call
        return parseInt(localStorage.getItem('cartCount') || '0');
    }

    // Enhanced Mobile Search Functions
    function initMobileSearch() {
        const searchToggle = document.querySelector('.search-toggle');
        const searchPanel = document.querySelector('.search-panel');
        const searchClose = document.querySelector('.search-close');
        const searchInput = document.querySelector('.mobile-search-input');
        const searchSubmit = document.querySelector('.search-submit');
        const suggestionTags = document.querySelectorAll('.suggestion-tag');

        if (searchToggle && searchPanel) {
            // Enhanced search toggle with animation
            searchToggle.addEventListener('click', () => {
                searchPanel.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Add entrance animation
                searchPanel.style.animation = 'slideInFromTop 0.3s ease-out';

                // Focus input with delay for better UX
                setTimeout(() => {
                    searchInput.focus();
                    if (navigator.vibrate) {
                        navigator.vibrate(30);
                    }
                }, 300);
            });

            searchClose.addEventListener('click', () => {
                searchPanel.style.animation = 'slideOutToTop 0.3s ease-in';

                setTimeout(() => {
                    searchPanel.classList.remove('active');
                    document.body.style.overflow = '';
                    searchInput.value = '';
                    searchPanel.style.animation = '';
                }, 300);
            });

            // Enhanced search functionality
            searchSubmit.addEventListener('click', () => {
                performSearch(searchInput.value);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch(searchInput.value);
                }
            });

            // Real-time search suggestions (debounced)
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (e.target.value.length > 2) {
                        showSearchSuggestions(e.target.value);
                    }
                }, 300);
            });

            // Enhanced suggestion tags
            suggestionTags.forEach(tag => {
                tag.addEventListener('click', () => {
                    searchInput.value = tag.textContent;
                    performSearch(tag.textContent);

                    // Add visual feedback
                    tag.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        tag.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && searchPanel.classList.contains('active')) {
                    searchClose.click();
                }
            });

            // Close on outside click
            searchPanel.addEventListener('click', (e) => {
                if (e.target === searchPanel) {
                    searchClose.click();
                }
            });
        }
    }

    function performSearch(query) {
        if (query.trim()) {
            // Add loading state
            const searchSubmit = document.querySelector('.search-submit');
            const originalHTML = searchSubmit.innerHTML;
            searchSubmit.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Simulate search delay
            setTimeout(() => {
                window.location.href = `<?= UrlHelper::url('/products?search=') ?>${encodeURIComponent(query)}`;
            }, 500);
        }
    }

    function showSearchSuggestions(query) {
        // Placeholder for real-time search suggestions
        // This would typically make an API call to get suggestions
        console.log('Showing suggestions for:', query);
    }

    // Enhanced Image Lazy Loading and Performance
    function initImageOptimization() {
        // Enhanced lazy loading with Intersection Observer
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;

                        // Add loading class for smooth transition
                        img.classList.add('loading');

                        // Load the image
                        img.onload = () => {
                            img.classList.add('loaded');
                            img.classList.remove('loading');

                            // Mark parent container as loaded
                            const container = img.closest('.product-image');
                            if (container) {
                                container.classList.add('loaded');
                            }
                        };

                        img.onerror = () => {
                            img.classList.add('error');
                            img.src = '<?= UrlHelper::url('/assets/img/placeholder.jpg') ?>';
                        };

                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });

            // Observe all lazy images
            document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Preload critical images
        const criticalImages = document.querySelectorAll('.hero img, .featured-card img');
        criticalImages.forEach(img => {
            if (img.loading !== 'lazy') {
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.as = 'image';
                preloadLink.href = img.src;
                document.head.appendChild(preloadLink);
            }
        });
    }

    // Performance optimization for mobile
    function initMobilePerformance() {
        // Reduce animations on low-end devices
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
            document.documentElement.classList.add('low-performance');
        }

        // Optimize scroll performance
        let ticking = false;

        function updateScrollEffects() {
            // Throttled scroll effects
            const scrolled = window.pageYOffset;

            // Update parallax effects only if visible
            const hero = document.querySelector('.hero');
            if (hero && scrolled < hero.offsetHeight) {
                const heroBackground = document.querySelector('.hero-background');
                if (heroBackground) {
                    heroBackground.style.transform = `translateY(${scrolled * 0.3}px)`;
                }
            }

            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        }, {
            passive: true
        });

        // Optimize touch events
        document.addEventListener('touchstart', () => {}, {
            passive: true
        });
        document.addEventListener('touchmove', () => {}, {
            passive: true
        });
    }

    // Content adaptation based on device capabilities
    function adaptContentForDevice() {
        const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
            navigator.deviceMemory <= 2 ||
            navigator.connection?.effectiveType === 'slow-2g' ||
            navigator.connection?.effectiveType === '2g';

        if (isLowEndDevice) {
            // Reduce visual effects for low-end devices
            document.documentElement.classList.add('reduced-effects');

            // Disable non-essential animations
            const style = document.createElement('style');
            style.textContent = `
                .reduced-effects .hero-floating-elements,
                .reduced-effects .floating-element,
                .reduced-effects .hero-particles {
                    display: none;
                }
                .reduced-effects .enhanced-card:hover {
                    transform: none;
                }
                .reduced-effects .product-overlay {
                    opacity: 1;
                    background: rgba(0, 0, 0, 0.6);
                }
            `;
            document.head.appendChild(style);
        }

        // Adapt grid based on screen size and performance
        const updateProductGrid = () => {
            const grids = document.querySelectorAll('.products-grid');
            const screenWidth = window.innerWidth;

            grids.forEach(grid => {
                if (screenWidth <= 320) {
                    grid.style.gridTemplateColumns = '1fr';
                } else if (screenWidth <= 480) {
                    grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                } else if (screenWidth <= 768) {
                    grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                } else if (screenWidth <= 1024) {
                    grid.style.gridTemplateColumns = 'repeat(3, 1fr)';
                }
            });
        };

        updateProductGrid();
        window.addEventListener('resize', updateProductGrid);
    }

    // Initialize all optimizations
    initImageOptimization();
    initMobilePerformance();
    adaptContentForDevice();

    // Initialize mobile features
    if (window.innerWidth <= 768) {
        initMobileNavigation();
        initMobileSearch();
    }

    // Re-initialize on window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 768) {
            initMobileNavigation();
            initMobileSearch();
        }
        adaptContentForDevice();
    });

    // Performance monitoring and optimization
    function initPerformanceMonitoring() {
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
            // This would integrate with web-vitals library if available
            console.log('Performance monitoring initialized');
        }

        // Monitor memory usage
        if (performance.memory) {
            const memoryInfo = performance.memory;
            if (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize > 0.8) {
                // High memory usage - reduce effects
                document.documentElement.classList.add('memory-constrained');
            }
        }

        // Monitor frame rate
        let frameCount = 0;
        let lastTime = performance.now();

        function measureFPS() {
            frameCount++;
            const currentTime = performance.now();

            if (currentTime - lastTime >= 1000) {
                const fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;

                // If FPS is low, reduce animations
                if (fps < 30) {
                    document.documentElement.classList.add('low-fps');
                }
            }

            requestAnimationFrame(measureFPS);
        }

        requestAnimationFrame(measureFPS);
    }

    // Optimize critical rendering path
    function optimizeCriticalPath() {
        // Preload critical resources
        const criticalResources = [
            '<?= UrlHelper::css('style.css') ?>',
            '<?= UrlHelper::url('/assets/fonts/main.woff2') ?>'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.includes('.css') ? 'style' : 'font';
            if (link.as === 'font') {
                link.type = 'font/woff2';
                link.crossOrigin = 'anonymous';
            }
            document.head.appendChild(link);
        });

        // Optimize font loading
        if ('fonts' in document) {
            document.fonts.ready.then(() => {
                document.documentElement.classList.add('fonts-loaded');
            });
        }
    }

    // Service Worker for caching (if supported)
    function initServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);

                        // Update available
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // Show update notification
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    function showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.innerHTML = `
            <div class="update-content">
                <span>New version available!</span>
                <button onclick="window.location.reload()">Update</button>
                <button onclick="this.parentElement.parentElement.remove()">Later</button>
            </div>
        `;
        document.body.appendChild(notification);
    }

    // Initialize all performance optimizations
    initPerformanceMonitoring();
    optimizeCriticalPath();
    initServiceWorker();

    // Add CSS animations for search panel and performance styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideOutToTop {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .mobile-bottom-nav {
            transition: transform 0.3s ease-in-out;
        }

        .nav-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(126, 87, 194, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        /* Performance-based optimizations */
        .low-fps .enhanced-card:hover,
        .memory-constrained .enhanced-card:hover {
            transform: none;
        }

        .low-fps .hero-floating-elements,
        .memory-constrained .hero-floating-elements {
            display: none;
        }

        .fonts-loaded .hero-title-accent {
            font-display: swap;
        }

        /* Update notification */
        .update-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-purple);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            animation: slideInFromRight 0.3s ease-out;
        }

        .update-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .update-content button {
            background: white;
            color: var(--primary-purple);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        @keyframes slideInFromRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Critical CSS optimizations */
        .hero {
            contain: layout style paint;
        }

        .product-card {
            contain: layout style;
        }

        /* Reduce motion for performance */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    `;
    document.head.appendChild(style);

    // Cross-Device Testing and Compatibility
    function initDeviceDetection() {
        const deviceInfo = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            screenWidth: screen.width,
            screenHeight: screen.height,
            devicePixelRatio: window.devicePixelRatio || 1,
            orientation: screen.orientation?.type || 'unknown',
            touchSupport: 'ontouchstart' in window,
            connectionType: navigator.connection?.effectiveType || 'unknown',
            memory: navigator.deviceMemory || 'unknown',
            cores: navigator.hardwareConcurrency || 'unknown'
        };

        // Detect device type
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTablet = /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/i.test(navigator.userAgent);
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        const isChrome = /Chrome/i.test(navigator.userAgent);
        const isFirefox = /Firefox/i.test(navigator.userAgent);

        // Add device classes to body
        document.body.classList.add(
            isMobile ? 'mobile-device' : 'desktop-device',
            isTablet ? 'tablet-device' : '',
            isIOS ? 'ios-device' : '',
            isAndroid ? 'android-device' : '',
            isSafari ? 'safari-browser' : '',
            isChrome ? 'chrome-browser' : '',
            isFirefox ? 'firefox-browser' : ''
        );

        // Device-specific optimizations
        if (isIOS) {
            // iOS-specific fixes
            document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);

            // Handle iOS viewport changes
            window.addEventListener('resize', () => {
                document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
            });

            // Fix iOS input zoom
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                    input.style.fontSize = '16px';
                }
            });
        }

        if (isAndroid) {
            // Android-specific optimizations
            document.body.classList.add('android-device');

            // Handle Android keyboard
            let initialViewportHeight = window.innerHeight;
            window.addEventListener('resize', () => {
                const currentHeight = window.innerHeight;
                if (currentHeight < initialViewportHeight * 0.75) {
                    document.body.classList.add('keyboard-open');
                } else {
                    document.body.classList.remove('keyboard-open');
                }
            });
        }

        // Log device info for debugging
        console.log('Device Info:', deviceInfo);

        return deviceInfo;
    }

    // Orientation change handling
    function initOrientationHandling() {
        function handleOrientationChange() {
            const orientation = screen.orientation?.type ||
                (window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');

            document.body.classList.remove('portrait', 'landscape');
            document.body.classList.add(orientation.includes('portrait') ? 'portrait' : 'landscape');

            // Recalculate layouts after orientation change
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        }

        // Listen for orientation changes
        if (screen.orientation) {
            screen.orientation.addEventListener('change', handleOrientationChange);
        } else {
            window.addEventListener('orientationchange', handleOrientationChange);
        }

        // Initial orientation
        handleOrientationChange();
    }

    // Cross-browser compatibility fixes
    function initCompatibilityFixes() {
        // Polyfill for CSS custom properties (IE11)
        if (!window.CSS || !CSS.supports('color', 'var(--fake-var)')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '<?= UrlHelper::url('/assets/css/ie-fallback.css') ?>';
            document.head.appendChild(link);
        }

        // Intersection Observer polyfill
        if (!('IntersectionObserver' in window)) {
            const script = document.createElement('script');
            script.src = 'https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver';
            document.head.appendChild(script);
        }

        // Object-fit polyfill for IE
        if (!('objectFit' in document.documentElement.style)) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/object-fit-images/3.2.4/ofi.min.js';
            script.onload = () => {
                if (window.objectFitImages) {
                    objectFitImages();
                }
            };
            document.head.appendChild(script);
        }

        // Fix for Safari's 100vh issue
        if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
            const style = document.createElement('style');
            style.textContent = `
                .hero {
                    height: 100vh;
                    height: calc(var(--vh, 1vh) * 100);
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Performance testing utilities
    function initPerformanceTesting() {
        // Measure page load performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.fetchStart;
                const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.fetchStart;

                console.log('Performance Metrics:', {
                    loadTime: `${loadTime}ms`,
                    domContentLoaded: `${domContentLoaded}ms`,
                    firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 'N/A',
                    firstContentfulPaint: performance.getEntriesByType('paint')[1]?.startTime || 'N/A'
                });

                // Send performance data to analytics (if implemented)
                if (window.gtag) {
                    gtag('event', 'page_load_time', {
                        value: Math.round(loadTime),
                        custom_parameter: navigator.userAgent
                    });
                }
            }, 0);
        });

        // Monitor long tasks
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > 50) {
                        console.warn('Long task detected:', entry);
                    }
                }
            });
            observer.observe({
                entryTypes: ['longtask']
            });
        }
    }

    // Debug utilities for testing
    function initDebugUtilities() {
        // Add debug panel for development
        if (window.location.hostname === 'localhost' || window.location.search.includes('debug=true')) {
            const debugPanel = document.createElement('div');
            debugPanel.id = 'debug-panel';
            debugPanel.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
                z-index: 10000;
                max-width: 300px;
                display: none;
            `;

            const toggleButton = document.createElement('button');
            toggleButton.textContent = 'Debug';
            toggleButton.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 10001;
                background: #007cba;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                cursor: pointer;
            `;

            toggleButton.onclick = () => {
                debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
                updateDebugInfo();
            };

            function updateDebugInfo() {
                debugPanel.innerHTML = `
                    <strong>Debug Info:</strong><br>
                    Screen: ${screen.width}x${screen.height}<br>
                    Viewport: ${window.innerWidth}x${window.innerHeight}<br>
                    DPR: ${window.devicePixelRatio}<br>
                    UA: ${navigator.userAgent.substring(0, 50)}...<br>
                    Connection: ${navigator.connection?.effectiveType || 'Unknown'}<br>
                    Memory: ${navigator.deviceMemory || 'Unknown'}GB<br>
                    Cores: ${navigator.hardwareConcurrency || 'Unknown'}<br>
                    Touch: ${('ontouchstart' in window) ? 'Yes' : 'No'}
                `;
            }

            document.body.appendChild(debugPanel);
            document.body.appendChild(toggleButton);
        }
    }

    // Initialize all cross-device features
    const deviceInfo = initDeviceDetection();
    initOrientationHandling();
    initCompatibilityFixes();
    initPerformanceTesting();
    initDebugUtilities();

    // Global error handling for better debugging
    window.addEventListener('error', (e) => {
        console.error('Global error:', e.error);
        // Could send to error tracking service
    });

    window.addEventListener('unhandledrejection', (e) => {
        console.error('Unhandled promise rejection:', e.reason);
        // Could send to error tracking service
    });

    console.log('✅ Cleanance Lab mobile optimization complete!');
    console.log('📱 Device compatibility initialized');
    console.log('🚀 Performance optimizations active');
</script>