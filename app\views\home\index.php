<!-- Enhanced Hero Section -->
<section class="hero">
    <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-gradient-overlay"></div>
    </div>

    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>Premium Quality Products</span>
                </div>

                <h1 class="hero-title">
                    <span class="hero-title-main">Transform Your World with</span>
                    <span class="hero-title-accent">Cleanance Lab</span>
                </h1>

                <p class="hero-subtitle">
                    Discover cutting-edge products crafted with precision and care. From innovative solutions to everyday essentials, we bring you the finest quality that elevates your lifestyle.
                </p>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <div class="hero-stat-number">10K+</div>
                        <div class="hero-stat-label">Happy Customers</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">500+</div>
                        <div class="hero-stat-label">Premium Products</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">99%</div>
                        <div class="hero-stat-label">Satisfaction Rate</div>
                    </div>
                </div>

                <div class="hero-actions">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-primary btn-lg hero-cta-primary">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Explore Products</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="<?= UrlHelper::url('/about') ?>" class="btn btn-outline btn-lg hero-cta-secondary">
                        <i class="fas fa-play-circle"></i>
                        <span>Watch Story</span>
                    </a>
                </div>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-shipping-fast"></i>
                        <span>Free Shipping</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure Payment</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-medal"></i>
                        <span>Premium Quality</span>
                    </div>
                </div>
            </div>

            <div class="hero-visual-section">
                <div class="hero-main-visual">
                    <div class="hero-product-showcase">
                        <div class="hero-product-card hero-product-1">
                            <div class="product-image">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Lab Essentials</span>
                                <span class="product-price">$29.99</span>
                            </div>
                        </div>

                        <div class="hero-product-card hero-product-2">
                            <div class="product-image">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Premium Solutions</span>
                                <span class="product-price">$49.99</span>
                            </div>
                        </div>

                        <div class="hero-product-card hero-product-3">
                            <div class="product-image">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Research Tools</span>
                                <span class="product-price">$89.99</span>
                            </div>
                        </div>
                    </div>

                    <div class="hero-floating-elements">
                        <div class="floating-element element-1">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="floating-element element-2">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="floating-element element-3">
                            <i class="fas fa-molecular"></i>
                        </div>
                        <div class="floating-element element-4">✨</div>
                        <div class="floating-element element-5">🧪</div>
                        <div class="floating-element element-6">⚗️</div>
                    </div>
                </div>

                <div class="hero-trust-indicators">
                    <div class="trust-indicator">
                        <i class="fas fa-certificate"></i>
                        <span>ISO Certified</span>
                    </div>
                    <div class="trust-indicator">
                        <i class="fas fa-award"></i>
                        <span>Award Winning</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="hero-scroll-indicator">
        <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
        </div>
        <span>Discover More</span>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Shop by Category</h2>
            <p class="section-subtitle">Explore our curated collection of products</p>
        </div>

        <div class="categories-grid">
            <?php foreach ($categories as $category): ?>
                <a href="<?= UrlHelper::url('/products/' . $category['slug']) ?>" class="category-card">
                    <div class="category-icon">
                        <?php
                        $icons = [
                            'electronics' => 'fas fa-mobile-alt',
                            'fashion' => 'fas fa-tshirt',
                            'home-garden' => 'fas fa-home',
                            'sports' => 'fas fa-dumbbell',
                            'books' => 'fas fa-book'
                        ];
                        $icon = $icons[$category['slug']] ?? 'fas fa-tag';
                        ?>
                        <i class="<?= $icon ?>"></i>
                    </div>
                    <h3 class="category-name"><?= htmlspecialchars($category['name']) ?></h3>
                    <p class="category-description"><?= htmlspecialchars($category['description'] ?? 'Explore amazing products') ?></p>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                Featured Products
            </h2>
            <p class="section-subtitle">Handpicked products just for you</p>
        </div>

        <div class="products-grid grid-cols-5">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="product-card fade-in compact-card featured-card">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <?php if ($product['sale_price']): ?>
                            <div class="product-badge sale-badge">
                                <span>-<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%</span>
                            </div>
                        <?php endif; ?>
                        <?php if ($product['is_featured']): ?>
                            <div class="product-badge featured-badge">
                                <i class="fas fa-star"></i>
                            </div>
                        <?php endif; ?>
                        <div class="product-overlay">
                            <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>" title="Add to Wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn btn-primary btn-sm add-to-cart-quick" data-product-id="<?= $product['id'] ?>" title="Add to Cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            <?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?>
                        </div>
                        <h3 class="product-title">
                            <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                                <span class="discount-amount">
                                    Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?= $i <= 4 ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-count">(<?= rand(10, 50) ?>)</span>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary add-to-cart"
                                data-product-id="<?= $product['id'] ?>"
                                data-quantity="1">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-outline btn-lg view-all-btn">
                <i class="fas fa-th-large"></i>
                View All Products
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- Latest Products Section -->
<section class="latest-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-clock"></i>
                Latest Arrivals
            </h2>
            <p class="section-subtitle">Fresh products added to our collection</p>
        </div>

        <div class="products-grid grid-cols-4">
            <?php foreach ($latestProducts as $product): ?>
                <div class="product-card fade-in compact-card latest-card">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <div class="product-badge new-badge">
                            <i class="fas fa-sparkles"></i>
                            New
                        </div>
                        <div class="product-overlay">
                            <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>" title="Add to Wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn btn-primary btn-sm add-to-cart-quick" data-product-id="<?= $product['id'] ?>" title="Add to Cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            <?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?>
                        </div>
                        <h3 class="product-title">
                            <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                                <span class="discount-amount">
                                    Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?= $i <= 4 ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-count">(<?= rand(5, 25) ?>)</span>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary add-to-cart"
                                data-product-id="<?= $product['id'] ?>"
                                data-quantity="1">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="features-grid grid-cols-4">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h3 class="feature-title">Fast Shipping</h3>
                <p class="feature-description">Free shipping on orders over $100</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">Secure Payment</h3>
                <p class="feature-description">100% secure payment processing</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-undo"></i>
                </div>
                <h3 class="feature-title">Easy Returns</h3>
                <p class="feature-description">30-day return policy</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3 class="feature-title">24/7 Support</h3>
                <p class="feature-description">Round the clock customer support</p>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section">
    <div class="container">
        <div class="newsletter-content">
            <div class="newsletter-text">
                <h2 class="newsletter-title">Stay Updated</h2>
                <p class="newsletter-description">Subscribe to our newsletter for the latest products, exclusive offers, and updates.</p>
            </div>
            <form class="newsletter-form">
                <input type="email" placeholder="Enter your email address" required>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Subscribe
                </button>
            </form>
        </div>
    </div>
</section>

<style>
    /* Enhanced Hero Section */
    .hero {
        min-height: 100vh;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;
        background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-card) 50%, var(--dark-surface) 100%);
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
    }

    .hero-particles {
        position: absolute;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(2px 2px at 20px 30px, rgba(126, 87, 194, 0.3), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(126, 87, 194, 0.2), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(126, 87, 194, 0.4), transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(126, 87, 194, 0.3), transparent),
            radial-gradient(2px 2px at 160px 30px, rgba(126, 87, 194, 0.2), transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: particleFloat 20s linear infinite;
    }

    .hero-gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 30%, rgba(126, 87, 194, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(126, 87, 194, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, rgba(126, 87, 194, 0.05) 0%, transparent 100%);
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        align-items: center;
        position: relative;
        z-index: 1;
        padding: var(--spacing-3xl) 0;
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-xl);
        font-size: var(--font-size-sm);
        font-weight: 600;
        margin-bottom: var(--spacing-lg);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
        animation: fadeInUp 0.8s ease-out;
    }

    .hero-title {
        margin-bottom: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .hero-title-main {
        display: block;
        font-size: var(--font-size-3xl);
        font-weight: 400;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-sm);
        line-height: 1.2;
    }

    .hero-title-accent {
        display: block;
        font-size: 4rem;
        font-weight: 800;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.1;
        margin-bottom: var(--spacing-md);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        line-height: 1.7;
        margin-bottom: var(--spacing-xl);
        max-width: 90%;
        animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    .hero-stats {
        display: flex;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        animation: fadeInUp 0.8s ease-out 0.6s both;
    }

    .hero-stat {
        text-align: center;
    }

    .hero-stat-number {
        font-size: var(--font-size-2xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .hero-stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .hero-actions {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        animation: fadeInUp 0.8s ease-out 0.8s both;
    }

    .hero-cta-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border: none;
        color: var(--white);
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-fast);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
        position: relative;
        overflow: hidden;
    }

    .hero-cta-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .hero-cta-primary:hover::before {
        left: 100%;
    }

    .hero-cta-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: var(--white);
    }

    .hero-cta-secondary {
        background: transparent;
        border: 2px solid var(--primary-purple);
        color: var(--primary-purple);
        padding: calc(var(--spacing-lg) - 2px) calc(var(--spacing-xl) - 2px);
        border-radius: var(--radius-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-fast);
    }

    .hero-cta-secondary:hover {
        background: var(--primary-purple);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .hero-features {
        display: flex;
        gap: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 1s both;
    }

    .hero-feature {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    .hero-feature i {
        color: var(--primary-purple);
        font-size: var(--font-size-base);
    }

    .hero-visual-section {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .hero-main-visual {
        position: relative;
        width: 100%;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-product-showcase {
        position: relative;
        width: 400px;
        height: 400px;
    }

    .hero-product-card {
        position: absolute;
        width: 120px;
        height: 140px;
        background: var(--dark-card);
        border: 2px solid var(--primary-purple);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.2);
        transition: all var(--transition-fast);
        animation: productFloat 6s ease-in-out infinite;
    }

    .hero-product-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .hero-product-2 {
        top: 10%;
        right: 15%;
        animation-delay: 2s;
    }

    .hero-product-3 {
        bottom: 20%;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 4s;
    }

    .hero-product-card:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.3);
    }

    .hero-product-card .product-image {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--font-size-xl);
    }

    .hero-product-card .product-info {
        text-align: center;
    }

    .hero-product-card .product-name {
        display: block;
        font-size: var(--font-size-xs);
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
    }

    .hero-product-card .product-price {
        display: block;
        font-size: var(--font-size-sm);
        color: var(--primary-purple);
        font-weight: 700;
    }

    .hero-floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .floating-element {
        position: absolute;
        font-size: var(--font-size-xl);
        color: var(--primary-purple);
        opacity: 0.6;
        animation: elementFloat 8s ease-in-out infinite;
    }

    .element-1 {
        top: 15%;
        left: 5%;
        animation-delay: 0s;
    }

    .element-2 {
        top: 25%;
        right: 5%;
        animation-delay: 1s;
    }

    .element-3 {
        bottom: 30%;
        left: 15%;
        animation-delay: 2s;
    }

    .element-4 {
        top: 60%;
        right: 25%;
        animation-delay: 3s;
    }

    .element-5 {
        bottom: 15%;
        right: 10%;
        animation-delay: 4s;
    }

    .element-6 {
        top: 40%;
        left: 80%;
        animation-delay: 5s;
    }

    .hero-trust-indicators {
        display: flex;
        gap: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 1.2s both;
    }

    .trust-indicator {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: rgba(126, 87, 194, 0.1);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-lg);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
        border: 1px solid rgba(126, 87, 194, 0.2);
    }

    .trust-indicator i {
        color: var(--primary-purple);
    }

    .hero-scroll-indicator {
        position: absolute;
        bottom: var(--spacing-xl);
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        animation: fadeInUp 0.8s ease-out 1.4s both;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .hero-scroll-indicator:hover {
        color: var(--primary-purple);
        transform: translateX(-50%) translateY(-5px);
    }

    .scroll-arrow {
        width: 40px;
        height: 40px;
        border: 2px solid currentColor;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: bounce 2s infinite;
    }

    /* Hero Animations */
    @keyframes particleFloat {
        0% {
            transform: translateY(0px) translateX(0px);
        }

        33% {
            transform: translateY(-10px) translateX(5px);
        }

        66% {
            transform: translateY(5px) translateX(-5px);
        }

        100% {
            transform: translateY(0px) translateX(0px);
        }
    }

    @keyframes productFloat {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        50% {
            transform: translateY(-15px) rotate(2deg);
        }
    }

    @keyframes elementFloat {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.6;
        }

        25% {
            transform: translateY(-10px) rotate(5deg);
            opacity: 0.8;
        }

        50% {
            transform: translateY(-5px) rotate(-3deg);
            opacity: 0.4;
        }

        75% {
            transform: translateY(-12px) rotate(7deg);
            opacity: 0.7;
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-10px);
        }

        60% {
            transform: translateY(-5px);
        }
    }

    /* Categories Section */
    .categories-section {
        padding: var(--spacing-2xl) 0;
        background-color: var(--dark-bg);
    }

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .category-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-fast);
    }

    .category-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
        color: var(--text-primary);
    }

    .category-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-2xl);
        color: var(--white);
    }

    .category-name {
        font-size: var(--font-size-xl);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .category-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Featured & Latest Sections */
    .featured-section,
    .latest-section {
        padding: var(--spacing-3xl) 0;
        position: relative;
    }

    .featured-section {
        background-color: var(--dark-card);
    }

    .latest-section {
        background-color: var(--dark-bg);
    }

    .featured-section .container,
    .latest-section .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Section Spacing */
    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-3xl);
        position: relative;
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .section-title i {
        color: var(--primary-purple);
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Grid Container Improvements */
    .products-grid {
        display: grid;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        align-items: start;
        justify-items: stretch;
        width: 100%;
    }

    .products-grid.grid-cols-5 {
        grid-template-columns: repeat(5, 1fr);
        max-width: 1200px;
        margin: 0 auto var(--spacing-xl);
    }

    .products-grid.grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1000px;
        margin: 0 auto var(--spacing-xl);
    }

    /* Ensure all cards have equal height */
    .products-grid .product-card {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    /* Compact Product Cards for Home Page */
    .compact-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-fast);
        box-shadow: var(--shadow-md);
    }

    .compact-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .compact-card .product-image {
        aspect-ratio: 1;
        height: 200px;
        position: relative;
        overflow: hidden;
    }

    .compact-card .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-fast);
    }

    .compact-card:hover .product-image img {
        transform: scale(1.05);
    }

    .compact-card .product-content {
        padding: var(--spacing-md);
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .compact-card .product-title {
        font-size: var(--font-size-sm);
        line-height: 1.3;
        margin-bottom: var(--spacing-xs);
        min-height: 2.6em;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .compact-card .product-category {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-xs);
        min-height: 1.2em;
    }

    .compact-card .product-price {
        margin-bottom: var(--spacing-sm);
        min-height: 1.5em;
    }

    .compact-card .product-price-current {
        font-size: var(--font-size-md);
        font-weight: 600;
    }

    .compact-card .product-price-original {
        font-size: var(--font-size-xs);
    }

    .compact-card .product-rating {
        margin-bottom: var(--spacing-sm);
        min-height: 1.2em;
    }

    .compact-card .product-actions {
        margin-top: auto;
        padding-top: var(--spacing-sm);
    }

    .compact-card .product-actions .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: opacity var(--transition-fast);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    /* Features Section */
    .features-section {
        padding: var(--spacing-2xl) 0;
        background-color: var(--dark-card);
    }

    .feature-card {
        text-align: center;
        padding: var(--spacing-lg);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-xl);
        color: var(--white);
    }

    .feature-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .feature-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Newsletter Section */
    .newsletter-section {
        padding: var(--spacing-2xl) 0;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        color: var(--white);
    }

    .newsletter-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
    }

    .newsletter-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-md);
    }

    .newsletter-description {
        font-size: var(--font-size-lg);
        opacity: 0.9;
        line-height: 1.6;
    }

    .newsletter-form {
        display: flex;
        gap: var(--spacing-sm);
    }

    .newsletter-form input {
        flex: 1;
        padding: var(--spacing-md);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--white);
    }

    .newsletter-form input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form input:focus {
        outline: none;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .newsletter-form .btn {
        background-color: var(--white);
        color: var(--primary-purple);
        border: none;
    }

    .newsletter-form .btn:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .grid-cols-5 {
            grid-template-columns: repeat(4, 1fr);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 1024px) {
        .grid-cols-5 {
            grid-template-columns: repeat(3, 1fr);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
        }

        .products-grid {
            gap: var(--spacing-lg);
        }
    }

    @media (max-width: 768px) {
        .hero {
            min-height: 90vh;
            padding: var(--spacing-xl) 0;
        }

        .hero-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
            text-align: center;
        }

        .hero-title-main {
            font-size: var(--font-size-xl);
        }

        .hero-title-accent {
            font-size: var(--font-size-4xl);
        }

        .hero-subtitle {
            font-size: var(--font-size-base);
            max-width: 100%;
        }

        .hero-stats {
            justify-content: center;
            gap: var(--spacing-lg);
        }

        .hero-actions {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            width: 100%;
            max-width: 280px;
            justify-content: center;
        }

        .hero-features {
            justify-content: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .hero-main-visual {
            height: 350px;
        }

        .hero-product-showcase {
            width: 300px;
            height: 300px;
        }

        .hero-product-card {
            width: 100px;
            height: 120px;
            padding: var(--spacing-sm);
        }

        .hero-product-card .product-image {
            width: 50px;
            height: 50px;
            font-size: var(--font-size-lg);
        }

        .hero-trust-indicators {
            justify-content: center;
            flex-wrap: wrap;
        }

        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .newsletter-content {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .newsletter-form {
            flex-direction: column;
        }

        /* Responsive Product Grids */
        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }

        .compact-card .product-image {
            height: 180px;
        }

        .compact-card .product-content {
            padding: var(--spacing-sm);
        }
    }

    @media (max-width: 480px) {
        .hero {
            min-height: 80vh;
        }

        .hero-content {
            gap: var(--spacing-lg);
            padding: var(--spacing-lg) 0;
        }

        .hero-badge {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-md);
        }

        .hero-title-main {
            font-size: var(--font-size-lg);
        }

        .hero-title-accent {
            font-size: var(--font-size-3xl);
        }

        .hero-subtitle {
            font-size: var(--font-size-sm);
        }

        .hero-stats {
            gap: var(--spacing-md);
        }

        .hero-stat-number {
            font-size: var(--font-size-xl);
        }

        .hero-stat-label {
            font-size: var(--font-size-xs);
        }

        .hero-actions {
            gap: var(--spacing-sm);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-sm);
        }

        .hero-features {
            gap: var(--spacing-sm);
        }

        .hero-feature {
            font-size: var(--font-size-xs);
        }

        .hero-main-visual {
            height: 250px;
        }

        .hero-product-showcase {
            width: 200px;
            height: 200px;
        }

        .hero-product-card {
            width: 80px;
            height: 100px;
            padding: var(--spacing-xs);
        }

        .hero-product-card .product-image {
            width: 40px;
            height: 40px;
            font-size: var(--font-size-base);
        }

        .hero-product-card .product-name {
            font-size: 10px;
        }

        .hero-product-card .product-price {
            font-size: var(--font-size-xs);
        }

        .floating-element {
            font-size: var(--font-size-base);
        }

        .trust-indicator {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
        }

        .hero-scroll-indicator {
            bottom: var(--spacing-md);
            font-size: var(--font-size-xs);
        }

        .scroll-arrow {
            width: 30px;
            height: 30px;
        }

        .categories-grid {
            grid-template-columns: 1fr;
        }

        .features-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        /* Mobile Product Grids */
        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .compact-card .product-image {
            height: 140px;
        }

        .compact-card .product-content {
            padding: var(--spacing-xs);
        }

        .compact-card .product-title {
            font-size: var(--font-size-xs);
            min-height: 2.4em;
        }

        .compact-card .product-actions .btn {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs);
        }
    }

    /* Enhanced Product Cards */
    .featured-card,
    .latest-card {
        position: relative;
        overflow: hidden;
        border: 1px solid var(--border-color);
        transition: all var(--transition-fast);
    }

    .featured-card {
        border-color: var(--primary-purple);
        box-shadow: 0 4px 20px rgba(126, 87, 194, 0.1);
    }

    .featured-card:hover {
        border-color: var(--primary-light);
        box-shadow: 0 8px 30px rgba(126, 87, 194, 0.2);
        transform: translateY(-8px);
    }

    .latest-card {
        border-color: var(--success-color);
        box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
    }

    .latest-card:hover {
        border-color: var(--success-color);
        box-shadow: 0 8px 30px rgba(16, 185, 129, 0.2);
        transform: translateY(-8px);
    }

    /* Enhanced Product Badges */
    .product-badge {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
        z-index: 2;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .sale-badge {
        background: linear-gradient(135deg, var(--error-color), #dc2626);
        color: white;
        animation: pulse 2s infinite;
    }

    .featured-badge {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: auto;
    }

    .new-badge {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        animation: bounce 2s infinite;
    }

    /* Product Ratings */
    .product-rating {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
    }

    .stars {
        display: flex;
        gap: 2px;
    }

    .stars i {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
    }

    .stars i.filled {
        color: #fbbf24;
    }

    .rating-count {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
    }

    /* Enhanced Product Overlay */
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: all var(--transition-fast);
        backdrop-filter: blur(2px);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    .product-overlay .btn {
        transform: translateY(20px);
        transition: all var(--transition-fast);
    }

    .product-card:hover .product-overlay .btn {
        transform: translateY(0);
    }

    .product-overlay .btn:nth-child(1) {
        transition-delay: 0.1s;
    }

    .product-overlay .btn:nth-child(2) {
        transition-delay: 0.2s;
    }

    .product-overlay .btn:nth-child(3) {
        transition-delay: 0.3s;
    }

    /* Enhanced Product Content */
    .product-category {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }

    .product-category i {
        color: var(--primary-purple);
        font-size: var(--font-size-xs);
    }

    .discount-amount {
        background: var(--success-color);
        color: white;
        padding: 2px var(--spacing-xs);
        border-radius: var(--radius-xs);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    /* Enhanced View All Button */
    .view-all-btn {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border: none;
        color: white;
        padding: var(--spacing-lg) var(--spacing-2xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        transition: all var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
    }

    .view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: white;
    }

    /* Animations */
    @keyframes pulse {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-5px);
        }

        60% {
            transform: translateY(-3px);
        }
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<script>
    // Enhanced Product Interactions and Hero Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Hero scroll indicator functionality
        const scrollIndicator = document.querySelector('.hero-scroll-indicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', function() {
                const categoriesSection = document.querySelector('.categories-section');
                if (categoriesSection) {
                    categoriesSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        }

        // Parallax effect for hero background
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const heroBackground = document.querySelector('.hero-background');

            if (hero && heroBackground) {
                const heroHeight = hero.offsetHeight;
                const scrollPercent = scrolled / heroHeight;

                if (scrollPercent <= 1) {
                    heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
                    hero.style.opacity = Math.max(0.3, 1 - scrollPercent * 0.7);
                }
            }
        });

        // Animate hero stats on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.hero-stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
                        const suffix = finalValue.replace(/[\d]/g, '');

                        animateNumber(stat, 0, numericValue, suffix, 2000);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);

        const heroStats = document.querySelector('.hero-stats');
        if (heroStats) {
            statsObserver.observe(heroStats);
        }
        // Add to Cart functionality
        document.querySelectorAll('.add-to-cart, .add-to-cart-quick').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                const quantity = this.dataset.quantity || 1;

                addToCart(productId, quantity);
            });
        });

        // Add to Wishlist functionality
        document.querySelectorAll('.add-to-wishlist').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                addToWishlist(productId);
            });
        });

        // Quick View functionality
        document.querySelectorAll('.quick-view').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                showQuickView(productId);
            });
        });

        // Staggered animation for product cards
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    });

    // Add to Cart Function
    function addToCart(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        fetch('<?= UrlHelper::url('/cart/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart successfully!', 'success');
                    updateCartCount();
                } else {
                    showNotification(data.message || 'Failed to add to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to cart', 'error');
            });
    }

    // Add to Wishlist Function
    function addToWishlist(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);

        fetch('<?= UrlHelper::url('/wishlist/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                    updateWishlistCount();
                } else {
                    showNotification(data.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to wishlist', 'error');
            });
    }

    // Quick View Function
    function showQuickView(productId) {
        // For now, redirect to product detail page
        // In a real implementation, this would show a modal
        window.location.href = `<?= UrlHelper::url('/product/') ?>${productId}`;
    }

    // Notification System
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ${getNotificationStyle(type)}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    function getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'exclamation-circle';
            case 'warning':
                return 'exclamation-triangle';
            default:
                return 'info-circle';
        }
    }

    function getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'background: linear-gradient(135deg, #10b981, #059669);';
            case 'error':
                return 'background: linear-gradient(135deg, #ef4444, #dc2626);';
            case 'warning':
                return 'background: linear-gradient(135deg, #f59e0b, #d97706);';
            default:
                return 'background: linear-gradient(135deg, #3b82f6, #2563eb);';
        }
    }

    // Number animation function for hero stats
    function animateNumber(element, start, end, suffix, duration) {
        const startTime = performance.now();

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (end - start) * easeOutQuart);

            element.textContent = current + suffix;

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // Enhanced button hover effects
    function addButtonEffects() {
        const buttons = document.querySelectorAll('.hero-cta-primary, .hero-cta-secondary');

        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Initialize enhanced effects
    addButtonEffects();

    // Update counters (placeholder functions)
    function updateCartCount() {
        // This would typically fetch the current cart count from the server
        console.log('Cart updated');
    }

    function updateWishlistCount() {
        // This would typically fetch the current wishlist count from the server
        console.log('Wishlist updated');
    }
</script>